import type { Metadata } from 'next';
import './globals.css';
import { urw, gravtrac } from './fonts';
import AuthInitializer from '@/components/AuthInitializer';

export const metadata: Metadata = {
  title: 'Netflix - 100 PLUS',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${urw.variable} ${gravtrac.variable} bg-black antialiased`}
      >
        {/* <AuthInitializer /> */}
        {children}
      </body>
    </html>
  );
}
